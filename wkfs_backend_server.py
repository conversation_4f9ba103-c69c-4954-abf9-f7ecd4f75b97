#!/usr/bin/env python3
"""
WKFS Backend Server
Handles API calls to Wolters Kluwer Financial Services to bypass CORS restrictions
"""

import json
import base64
import requests
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# WKFS API endpoints
AUTH_HOST = "https://ct-admin.wolterskluwerfs.com"
DOC_HOST = "https://ct-docgensvcs.wolterskluwerfs.com"

@app.route('/api/auth', methods=['POST'])
def authenticate():
    """Authenticate with WKFS and get access token"""
    try:
        data = request.get_json()
        credentials = data.get('credentials', {})
        scope = data.get('scope', '')
        
        logger.info(f"Authentication request for client: {credentials.get('client_id', 'Unknown')}")
        
        # Prepare headers
        headers = {
            "WKFS-ClientCertificate": credentials.get('wkfs_client_certificate', ''),
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        # Prepare payload
        payload = {
            "grant_type": credentials.get('grant_type', ''),
            "client_id": credentials.get('client_id', ''),
            "scope": scope
        }
        
        # Make request to WKFS
        response = requests.post(
            f"{AUTH_HOST}/STS/connect/token",
            headers=headers,
            data=payload,
            timeout=30
        )
        
        logger.info(f"WKFS auth response status: {response.status_code}")
        
        if response.status_code == 200:
            auth_data = response.json()
            return jsonify({
                'success': True,
                'access_token': auth_data.get('access_token'),
                'token_type': auth_data.get('token_type', 'Bearer'),
                'expires_in': auth_data.get('expires_in')
            })
        else:
            logger.error(f"Authentication failed: {response.status_code} - {response.text}")
            return jsonify({
                'success': False,
                'error': f"Authentication failed: {response.status_code}",
                'details': response.text
            }), response.status_code
            
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/generate', methods=['POST'])
def generate_document():
    """Generate document via WKFS API"""
    try:
        data = request.get_json()
        access_token = data.get('access_token')
        xml_content = data.get('xml_content')
        config = data.get('config', {})
        selected_package = data.get('selected_package')
        account_id = data.get('account_id')
        
        logger.info(f"Document generation request for package: {selected_package}")
        
        # Convert XML to base64
        base64_data = base64.b64encode(xml_content.encode('utf-8')).decode('utf-8')
        logger.info(f"XML converted to base64 ({len(base64_data)} characters)")
        
        # Find package details
        packages = config.get('packages', [])
        package_info = None
        for pkg in packages:
            if pkg.get('name') == selected_package:
                package_info = pkg
                break
        
        if not package_info:
            return jsonify({
                'success': False,
                'error': f"Package '{selected_package}' not found in configuration"
            }), 400
        
        # Prepare request
        wkfs_id = config.get('wkfs_id')
        content_identifier = f"expere://{wkfs_id}/{package_info.get('package_or_packet')}"
        
        request_payload = {
            "documentFormat": "PDF",
            "ancillaryOutput": [
                {
                    "outputType": "ESignatureAndFieldSupport",
                    "eSignatureAndFieldSupport": {
                        "eSignatureCoordinatesOnly": True,
                        "eSignatureDateSupport": True,
                        "eSignatureTooltip": "Kindly Sign here",
                        "eSignatureInitialsTooltip": "Kindly put your initials here",
                        "nonSignatureFieldCoordinatesOnly": True,
                        "eSignatureWKES": False
                    }
                }
            ],
            "transactionData": base64_data,
            "contentIdentifier": content_identifier,
            "ezConfig": config.get('ezConfig', {})
        }
        
        payload = {
            "generate": {"request": request_payload}
        }
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        logger.info(f"Making document generation request to WKFS")
        logger.info(f"Content Identifier: {content_identifier}")
        
        # Make request to WKFS
        response = requests.post(
            f"{DOC_HOST}/DocumentService/api/v1/Document/account/{account_id}/generate-synchronous",
            headers=headers,
            json=payload,
            timeout=60
        )
        
        logger.info(f"WKFS generation response status: {response.status_code}")
        
        if response.status_code == 200:
            doc_data = response.json()
            return jsonify({
                'success': True,
                'data': doc_data
            })
        else:
            logger.error(f"Document generation failed: {response.status_code} - {response.text}")
            return jsonify({
                'success': False,
                'error': f"Document generation failed: {response.status_code}",
                'details': response.text
            }), response.status_code
            
    except Exception as e:
        logger.error(f"Document generation error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'WKFS Backend Server',
        'version': '1.0.0'
    })

@app.route('/', methods=['GET'])
def index():
    """Root endpoint"""
    return jsonify({
        'message': 'WKFS Backend Server is running',
        'endpoints': {
            'auth': '/api/auth',
            'generate': '/api/generate',
            'health': '/api/health'
        }
    })

if __name__ == '__main__':
    print("Starting WKFS Backend Server...")
    print("Server will run on http://localhost:8000")
    print("Make sure to install dependencies: pip install flask flask-cors requests")
    app.run(host='0.0.0.0', port=8000, debug=True)
