# WKFS Document Manager

A professional web interface for generating documents using Wolters Kluwer Financial Services (WKFS) APIs.

## 🚀 Quick Start

### Step 1: Start the Backend Server

**Windows:**
```bash
# Double-click the file or run in Command Prompt
start_backend.bat
```

**Mac/Linux:**
```bash
# Make executable and run
chmod +x start_backend.sh
./start_backend.sh
```

**Manual Start:**
```bash
# Install dependencies
pip install flask flask-cors requests

# Start server
python wkfs_backend_server.py
```

### Step 2: Open the Web Interface

Open `wkfs_document_manager.html` in your web browser.

The interface will show a green status indicator when the backend server is running.

## 📋 How to Use

### 1. **Generate Document (Default Tab)**
- **XML Input**: Paste your transaction XML in the large text area
- **Package Selection**: "Closing" is selected by default, click "Change Package" if needed
- **Generate**: Click "Generate Document" to create the PDF
- **Results**: View logs and download the generated PDF

### 2. **Package Selection**
- Browse available packages in card format
- Click on a package to select it
- Selected package is highlighted and shown on the Execute tab

### 3. **Configuration**
- Edit the configuration JSON (scope, wkfs_id, packages, etc.)
- Use "Format JSON" to clean up formatting
- Save changes to apply new configuration

### 4. **Credentials**
- Edit credentials JSON (client_id, account_id, certificates)
- Least frequently changed settings
- Save changes to apply new credentials

## 🔧 Technical Details

### Backend Server
- **Purpose**: Handles WKFS API calls to bypass browser CORS restrictions
- **Port**: Runs on `http://localhost:8000`
- **Endpoints**:
  - `/api/auth` - Authentication with WKFS
  - `/api/generate` - Document generation
  - `/api/health` - Server health check

### API Flow
1. **Authentication**: Gets access token from WKFS using client credentials
2. **XML Processing**: Converts XML to base64 format
3. **Document Generation**: Sends request to WKFS with package details
4. **PDF Processing**: Extracts PDF from response and makes it downloadable

### File Structure
```
├── wkfs_document_manager.html    # Main web interface
├── wkfs_backend_server.py        # Python backend server
├── start_backend.bat             # Windows startup script
├── start_backend.sh              # Mac/Linux startup script
├── wkfs_script_dlap/            # Original script files
│   ├── configuration.json       # WKFS configuration
│   ├── credentials.json         # WKFS credentials
│   ├── wkfs.xml                 # Sample XML file
│   └── wkfs_python_script.py    # Original Python script
└── README.md                    # This file
```

## 🎯 Key Features

- **Professional UI**: Clean, Apple-style design
- **Real API Integration**: Makes actual calls to Wolters Kluwer
- **Easy Copy-Paste**: Large text areas with copy buttons
- **Default Package**: "Closing" package pre-selected
- **Single Document**: Shows only the document returned by WKFS
- **Real-time Logs**: Live execution logs and status updates
- **Error Handling**: Detailed error messages and recovery options
- **Backend Status**: Visual indicator of server status

## 🔍 Troubleshooting

### Backend Server Issues
- **Red Status**: Backend server not running
- **Solution**: Start the backend server using the startup scripts
- **Port Conflict**: If port 8000 is in use, modify the port in `wkfs_backend_server.py`

### API Errors
- **Authentication Failed**: Check credentials in the Credentials tab
- **Package Not Found**: Verify package name in Configuration tab
- **Network Errors**: Check internet connection and WKFS service status

### Browser Issues
- **CORS Errors**: Make sure backend server is running
- **JavaScript Errors**: Check browser console for detailed error messages
- **File Access**: Some browsers may block local file access - use a local web server if needed

## 📝 Configuration Priority

Settings are prioritized by frequency of change:

1. **XML Content** (Most frequent) - Main input on Execute tab
2. **Package Selection** - Second tab, easy to change
3. **Configuration** - Third tab, occasional changes
4. **Credentials** (Least frequent) - Last tab, rarely changed

## 🔒 Security Notes

- Credentials are handled securely through the backend server
- No sensitive data is stored in browser localStorage
- All API calls go through the local backend to avoid CORS issues
- Client certificates are handled server-side only

## 📞 Support

If you encounter issues:

1. Check the backend server status indicator
2. Review the execution logs for detailed error messages
3. Verify your configuration and credentials
4. Ensure the WKFS services are accessible from your network
