<Txn xmlns="http://schemas.bankerssystems.com/2004/ExpereTxn">

    <Borrowers>

        <Borrower index="1">

            <SBARequiredDocuments>

                <SBARequiredDocument>
                    <DocumentDesc>Supporting documentation required for <PERSON><PERSON> to evaluate your
                        reacceptance request.</DocumentDesc>
                </SBARequiredDocument>

                <SBARequiredDocument>
                    <DocumentDesc>Bank Statement(s)</DocumentDesc>
                </SBARequiredDocument>

            </SBARequiredDocuments>


            <MultipleShareholdersInd>0</MultipleShareholdersInd>
            <HOAInd>0</HOAInd>
            <DateOfBirth>****-05-13</DateOfBirth>
            <CitizenType>1</CitizenType>
            <EmailAddress>p*****@t***.com</EmailAddress>
            <PhotocopyOfImmigrationDocumentAttachedToFormG845Ind>1</PhotocopyOfImmigrationDocumentAttachedToFormG845Ind>
            <G845OtherBenefitInd>1</G845OtherBenefitInd>
            <G845OtherBenefitDesc>SBA Disaster Loan</G845OtherBenefitDesc>
            <RembrandtID>5d541e73-4566-48df-ad16-195663fb2724</RembrandtID>

            <Phones>

                <Phone index="1">
                    <PhoneNumber>**********</PhoneNumber>
                    <PrimaryPhoneInd>1</PrimaryPhoneInd>
                    <Desc>Phone Number</Desc>
                </Phone>

            </Phones>


            <MaritalStatus>Unmarried</MaritalStatus>
            <MailingAddressCity>LEAGUE CITY</MailingAddressCity>
            <MailingAddressPostalCode>77573</MailingAddressPostalCode>
            <MailingAddressStreetAddr1>2014 CATAMARAN DR</MailingAddressStreetAddr1>
            <MailingAddressState>TX</MailingAddressState>
            <PermanentAddressStreetAddress1>2014 CATAMARAN DR</PermanentAddressStreetAddress1>
            <PermanentAddressCity>LEAGUE CITY</PermanentAddressCity>
            <PermanentAddressState>TX</PermanentAddressState>
            <PermanentAddressPostalCode>77573</PermanentAddressPostalCode>
            <PartyType>1</PartyType>
            <FirstName>LARA</FirstName>
            <LastName>SNOW</LastName>
            <SSN>666829265</SSN>
            <Signers>

                <Signer index="1">
                    <Selected>1</Selected>
                    <Name>LARA SNOW</Name>
                    <AuthorizationSignerInd>0</AuthorizationSignerInd>
                    <AuthorizedToSignInd>0</AuthorizedToSignInd>
                    <EmailAddress>p*****@t***.com</EmailAddress>
                    <OfficeHolderInd>1</OfficeHolderInd>
                    <SignerID>5d541e73-4566-48df-ad16-195663fb2724</SignerID>
                </Signer>

            </Signers>


            <AuthorityDocumentInd>1</AuthorityDocumentInd>
            <AuthorityNoteInformationType>2</AuthorityNoteInformationType>
            <LoanDecisionActionReportingAgencyInd>0</LoanDecisionActionReportingAgencyInd>
            <SBANoticeOfActionTakenSecondDeclineInd>0</SBANoticeOfActionTakenSecondDeclineInd>


        </Borrower>

    </Borrowers>


    <OrgAddresses>

        <OrgAddress index="1">
            <Value>14925 KINGSPORT ROAD</Value>
        </OrgAddress>

    </OrgAddresses>

    <SBASAVEProgramInd>1</SBASAVEProgramInd>
    <DateECOANoticeOfAction>2025-07-21</DateECOANoticeOfAction>
    <OrgEmailAddress>d*****@s***.gov</OrgEmailAddress>
    <OrgPostalCode>76155</OrgPostalCode>
    <!--
    <FormCertificationOfBeneficialOwnersInstitutionContactName></FormCertificationOfBeneficialOwnersInstitutionContactName>-->
    <!--
    <FormCertificationOfBeneficialOwnersInstitutionContactPhoneNumber></FormCertificationOfBeneficialOwnersInstitutionContactPhoneNumber> -->
    <OrgPhone>************</OrgPhone>
    <OrgExemptInd>1</OrgExemptInd>
    <!-- <HighlightSignaturesInd></HighlightSignaturesInd> -->
    <!-- <TaglineBarcodeDesc></TaglineBarcodeDesc> -->
    <!-- <TaglineUserDefinedLeftDesc>202307x20250717</TaglineUserDefinedLeftDesc>-->

    <!-- START: Tags for UCC Filing -->

    <OrgAdditionalAddresses>

        <OrgAdditionalAddress index="1">
            <OrgAdditionalAddressType>9</OrgAdditionalAddressType>
            <OrgContactCity>FORT WORTH</OrgContactCity>
            <OrgContactDepartmentAttentionDesc>Application Processing Department</OrgContactDepartmentAttentionDesc>
            <OrgContactPostalCode>76155</OrgContactPostalCode>
            <OrgContactState>TX</OrgContactState>
            <OrgContactStreetAddress>14925 KINGSPORT ROAD</OrgContactStreetAddress>
            <OrgContactTollFreeNumber>************</OrgContactTollFreeNumber>
        </OrgAdditionalAddress>

        <OrgAdditionalAddress index="2">
            <OrgAdditionalAddressType>19</OrgAdditionalAddressType>
            <OrgContactCity>FORT WORTH</OrgContactCity>
            <OrgContactDepartmentAttentionDesc>Disaster Assistance, Processing and Disbursement
                Center</OrgContactDepartmentAttentionDesc>
            <OrgContactPhoneNumber>************</OrgContactPhoneNumber>
            <OrgContactPostalCode>76155</OrgContactPostalCode>
            <OrgContactState>TX</OrgContactState>
            <OrgContactStreetAddress>14925 KINGSPORT ROAD</OrgContactStreetAddress>
            <OrgContactTollFreeNumber>************</OrgContactTollFreeNumber>
        </OrgAdditionalAddress>

        <OrgAdditionalAddress index="3">
            <OrgContactCity>ALLEN</OrgContactCity>
            <OrgContactPostalCode>75013</OrgContactPostalCode>
            <OrgContactState>TX</OrgContactState>
            <OrgContactStreetAddress>P.O. BOX 4500</OrgContactStreetAddress>
            <OrgContactTollFreeNumber>************</OrgContactTollFreeNumber>
        </OrgAdditionalAddress>

        <OrgAdditionalAddress index="4">
            <OrgAdditionalAddressType>7</OrgAdditionalAddressType>
            <OrgContactCity>FORT WORTH</OrgContactCity>
            <OrgContactDepartmentAttentionDesc>Disaster Assistance, Processing and Disbursement
                Center</OrgContactDepartmentAttentionDesc>
            <OrgContactPhoneNumber>************</OrgContactPhoneNumber>
            <OrgContactPostalCode>76155</OrgContactPostalCode>
            <OrgContactState>TX</OrgContactState>
            <OrgContactStreetAddress>14925 KINGSPORT ROAD</OrgContactStreetAddress>
            <OrgContactTollFreeNumber>************</OrgContactTollFreeNumber>
        </OrgAdditionalAddress>

        <OrgAdditionalAddress index="5">
            <OrgAdditionalAddressType>81</OrgAdditionalAddressType>
            <OrgContactCity>FORT WORTH</OrgContactCity>
            <OrgContactDepartmentAttentionDesc>Disaster Assistance, Processing and Disbursement
                Center</OrgContactDepartmentAttentionDesc>
            <OrgContactPhoneNumber>************</OrgContactPhoneNumber>
            <OrgContactPostalCode>76155</OrgContactPostalCode>
            <OrgContactState>TX</OrgContactState>
            <OrgContactStreetAddress>P.O. BOX 156089</OrgContactStreetAddress>
            <OrgContactTollFreeNumber>************</OrgContactTollFreeNumber>
        </OrgAdditionalAddress>

        <OrgAdditionalAddress index="6">
            <OrgAdditionalAddressType>39</OrgAdditionalAddressType>
            <OrgContactCity>EL PASO</OrgContactCity>
            <OrgContactCounty>EL PASO</OrgContactCounty>
            <OrgContactDepartmentAttentionDesc>U.S. SMALL BUSINESS ADMINISTRATION</OrgContactDepartmentAttentionDesc>
            <OrgContactPhoneNumber>************</OrgContactPhoneNumber>
            <OrgContactPostalCode>79925</OrgContactPostalCode>
            <OrgContactSecondLineAddress>SUITE 202</OrgContactSecondLineAddress>
            <OrgContactState>TX</OrgContactState>
            <OrgContactStreetAddress>1545 HAWKINS BLVD</OrgContactStreetAddress>
        </OrgAdditionalAddress>

        <OrgAdditionalAddress index="7">
            <OrgContactCity>El Paso</OrgContactCity>
            <OrgContactPhoneNumber>6672194963</OrgContactPhoneNumber>
            <OrgContactPostalCode>79925</OrgContactPostalCode>
            <OrgContactState>TX</OrgContactState>
            <OrgContactStreetAddress>1545 Hawkins Blvd., Suite 202&lt;br&gt;El Paso, TX 79925</OrgContactStreetAddress>
        </OrgAdditionalAddress>

    </OrgAdditionalAddresses>

    <Notes>


        <Note index="1">
            <InterestDeferralPeriodMonthsCount>12</InterestDeferralPeriodMonthsCount>
            <ProjectedPayments>

                <ProjectedPayment index="1">
                    <IntegratedDisclosureType>1</IntegratedDisclosureType>
                    <PaymentRangeIndicator>0</PaymentRangeIndicator>
                    <ProjectedPaymentMIPaymentAmount>0</ProjectedPaymentMIPaymentAmount>
                    <ProjectedPaymentCalculationPeriodStartNumber>2</ProjectedPaymentCalculationPeriodStartNumber>
                </ProjectedPayment>

            </ProjectedPayments>


            <TILExamples>

                <TILExample index="1">
                    <Type>1</Type>
                </TILExample>

            </TILExamples>


            <IntegratedDisclosureProductDesc>Fixed Rate</IntegratedDisclosureProductDesc>
            <LoanIdentifier>25069257425</LoanIdentifier>
            <ApplicationReceivedDate>2025-07-16</ApplicationReceivedDate>
            <OpenEndCreditInd>0</OpenEndCreditInd>
            <AdvanceOptionType>Single Advance</AdvanceOptionType>
            <TRID2018Ind>1</TRID2018Ind>
            <LoanDesc>Disaster Home Loan</LoanDesc>
            <LoanDecisionDenialLetterStyleInd>1</LoanDecisionDenialLetterStyleInd>
            <ActionTakenDesc>your application was denied</ActionTakenDesc>
            <LoanDecisionType>Withdrawn</LoanDecisionType>
            <InitialRateDefinitionType>Market Rate</InitialRateDefinitionType>
            <WrittenFormNoticeOfActionTakenCommercialInd>1</WrittenFormNoticeOfActionTakenCommercialInd>
            <PurposeType>Commercial</PurposeType>
            <StageType>New</StageType>
            <PaymentSchedules>

                <PaymentSchedule index="1">
                    <PmtType>Regular Payment</PmtType>
                </PaymentSchedule>

            </PaymentSchedules>


            <Borrowers>

                <Borrower index="1">
                    <IDRef>5d541e73-4566-48df-ad16-195663fb2724</IDRef>
                </Borrower>

            </Borrowers>

            <SBALoanName>Disaster Home Loan Application</SBALoanName>
            <AmendedAndRestatedInd>0</AmendedAndRestatedInd>
            <RateInfoItems>

                <RateInfoItem index="1">
                    <RateType>Fixed</RateType>
                    <InitialRateDeterminationType>2</InitialRateDeterminationType>
                </RateInfoItem>

            </RateInfoItems>

            <NoteAdvanceType>1</NoteAdvanceType>
            <MortgageType>999</MortgageType>
            <MortgageTypeOtherDesc>SBA</MortgageTypeOtherDesc>
            <IntentToProceedInd>1</IntentToProceedInd>
            <MonthsToFirstPrincipalPaymentDateNumber>1</MonthsToFirstPrincipalPaymentDateNumber>
            <Payments>

                <Payment index="1">
                    <CommercialPeriodicPaymentType>3</CommercialPeriodicPaymentType>
                    <PaymentFrequency>Monthly</PaymentFrequency>
                    <FirstPaymentDate>1 months from initial disbursement</FirstPaymentDate>
                    <RepayMethod>Installment</RepayMethod>
                    <AccrualMethodType>Amortized</AccrualMethodType>
                </Payment>

            </Payments>

            <NoteID>84cdc151-17ef-4048-8a86-ef7081220989</NoteID>
            <PartialPaymentsApplicationMethodType>1</PartialPaymentsApplicationMethodType>
            <LoanIdentifierIncludedInd>0</LoanIdentifierIncludedInd>
            <FeesAndChargesBaseFees>

                <FeesAndChargesBaseFee index="1">
                    <FeeName>SBA Closing Fees</FeeName>
                    <LoanEstimateTransferTaxTotalAmount>0</LoanEstimateTransferTaxTotalAmount>
                    <TotalAmount>0</TotalAmount>
                    <CalculatedAmount>0</CalculatedAmount>
                    <FixedAmount>0</FixedAmount>
                    <FeeShortName>Closing Fees</FeeShortName>
                    <LoanEstimateAmount>0</LoanEstimateAmount>
                    <LoanEstimateSectionType>8</LoanEstimateSectionType>
                    <ClosingDisclosureSectionType>8</ClosingDisclosureSectionType>
                    <Payments>

                        <Payment index="1">
                            <PACAmount>0</PACAmount>
                        </Payment>

                    </Payments>

                </FeesAndChargesBaseFee>

            </FeesAndChargesBaseFees>

            <Disbursements>

                <Disbursement index="1">
                    <Amount>0</Amount>
                    <AmountNotClosingCostInd>0</AmountNotClosingCostInd>
                    <Type>0</Type>
                </Disbursement>

            </Disbursements>

        </Note>

    </Notes>


    <CombinedNoteSecurityAgreementDisclosureInd>0</CombinedNoteSecurityAgreementDisclosureInd>
    <CombineNote>0</CombineNote>
    <UseCombinedTruthInLendingDwellingInd>1</UseCombinedTruthInLendingDwellingInd>
    <CreateRegulationZDisclosuresInd>1</CreateRegulationZDisclosuresInd>
    <ClosingInstructionsInd>0</ClosingInstructionsInd>
    <FormClosingInstructionsDocumentListInd>1</FormClosingInstructionsDocumentListInd>
    <MilitaryLendingActAppliesInd>0</MilitaryLendingActAppliesInd>
    <WillServiceMLoans>1</WillServiceMLoans>
    <ClosingInstructions>

        <ClosingInstruction index="1">
            <DocumentDate>2025-07-21</DocumentDate>
        </ClosingInstruction>

    </ClosingInstructions>


    <SettlementAgents>

        <SettlementAgent index="1">
            <Name>STEWART LENDER SERVICES</Name>
            <StreetAddress>500 N BROADWAY ST</StreetAddress>
            <StreetAddress2>SUITE 900</StreetAddress2>
            <City>ST LOUIS</City>
            <State>MO</State>
            <PostalCode>63102</PostalCode>
            <Phone>************</Phone>
            <EmailAddress>S*****@s***.com</EmailAddress>
            <SBAState>MO</SBAState>
            <LicenseIdentifier>1877196</LicenseIdentifier>
            <LicenseIssuingAuthorityState>TX</LicenseIssuingAuthorityState>
        </SettlementAgent>

    </SettlementAgents>


    <LoanAuthorizationAndAgreements>

        <LoanAuthorizationAndAgreement index="1">
            <DeadlinePeriodNumber>60</DeadlinePeriodNumber>
            <DeadlinePeriodType>days</DeadlinePeriodType>
            <IncludeFEMALanguageInd>0</IncludeFEMALanguageInd>

            <BorrowersCertifications>

                <BorrowersCertification index="1">
                    <CompensationDocumentDesc>SBA Form 5c, Disaster Home Loan Application</CompensationDocumentDesc>
                    <LoanSecuredByRealEstateInd>0</LoanSecuredByRealEstateInd>
                    <LoanSecuredByRealEstateOrManufacturedHomeInd>0</LoanSecuredByRealEstateOrManufacturedHomeInd>
                </BorrowersCertification>

            </BorrowersCertifications>


            <Disbursements>

                <Disbursement index="1">
                    <PeriodMonthsNumber>6</PeriodMonthsNumber>
                </Disbursement>

            </Disbursements>


            <UseOfProceeds>

                <UseOfProceed index="1">
                    <ProceedsLineItems>

                        <ProceedsLineItem index="1">

                            <Amount>0</Amount>
                        </ProceedsLineItem>

                    </ProceedsLineItems>


                </UseOfProceed>

                <UseOfProceed index="2">
                    <ProceedsLineItems>

                        <ProceedsLineItem index="1">

                            <Amount>0</Amount>
                        </ProceedsLineItem>

                    </ProceedsLineItems>


                </UseOfProceed>

            </UseOfProceeds>


            <RequirementsRelativeToCollaterals>

                <RequirementRelativeToCollateral index="1">
                    <MaximumUnsecuredLimitAmount>25000.00</MaximumUnsecuredLimitAmount>
                    <IncludeSectionInd>0</IncludeSectionInd>
                </RequirementRelativeToCollateral>

            </RequirementsRelativeToCollaterals>


            <IntegratedDisclosureDetails>

                <IntegratedDisclosureDetail index="1">
                    <IntegratedDisclosureType>1</IntegratedDisclosureType>
                    <IssuedDate>2025-07-21</IssuedDate>
                </IntegratedDisclosureDetail>

            </IntegratedDisclosureDetails>


        </LoanAuthorizationAndAgreement>

    </LoanAuthorizationAndAgreements>


    <IntegratedDisclosureDetails>

        <IntegratedDisclosureDetail index="1">
            <IntegratedDisclosureType>1</IntegratedDisclosureType>
            <IssuedDate>2025-07-21</IssuedDate>
            <ProjectedPaymentsEstimatedTaxesInsuranceAssessmentTotalAmount>0</ProjectedPaymentsEstimatedTaxesInsuranceAssessmentTotalAmount>
        </IntegratedDisclosureDetail>

    </IntegratedDisclosureDetails>


    <CashToCloseItems>

        <CashToCloseItem index="1">
            <CashToCloseItemType>12</CashToCloseItemType>
        </CashToCloseItem>

        <CashToCloseItem index="2">
            <CashToCloseItemType>3</CashToCloseItemType>
        </CashToCloseItem>

    </CashToCloseItems>


    <SBADamagedProperties>

        <SBADamagedProperty index="1">
            <DamagesLocatedInCNMIInd>0</DamagesLocatedInCNMIInd>
            <DamagesLocatedInGuamInd>0</DamagesLocatedInGuamInd>

            <Addresses>

                <Address index="1">
                    <ID>40a3ad10-7247-4eec-888c-df61713e9a8e</ID>
                    <City>LEAGUE CITY</City>
                    <State>TX</State>
                    <SBAState>TX</SBAState>
                    <PostalCode>77573</PostalCode>
                    <StreetAddress>2014 CATAMARAN DR</StreetAddress>
                </Address>

            </Addresses>

        </SBADamagedProperty>

    </SBADamagedProperties>


    <!-- END: Tags for UCC Filing -->

    <!-- START: Tags for Security Commercial> -->
    <FormSBAFeesDisclosureInd>0</FormSBAFeesDisclosureInd>
    <FormSBAFeesDisclosureIncludeSignaturesInd>1</FormSBAFeesDisclosureIncludeSignaturesInd>
    <SBAInterestRateOptionType>1</SBAInterestRateOptionType>
    <SBAMaturityDateBasedOnType>1</SBAMaturityDateBasedOnType>
    <OrgCity>FORT WORTH</OrgCity>
    <OrgDesc>Government Agency</OrgDesc>
    <OrgEstablishedState>USA</OrgEstablishedState>
    <OrgLegalName>U.S. SMALL BUSINESS ADMINISTRATION</OrgLegalName>
    <OrgState>TX</OrgState>
    <OrgTermType>Other</OrgTermType>
    <PartyRequestingRecordingCity>FORT WORTH</PartyRequestingRecordingCity>
    <PartyRequestingRecordingName>U.S. SMALL BUSINESS ADMINISTRATION</PartyRequestingRecordingName>
    <PartyRequestingRecordingPostalCode>76155</PartyRequestingRecordingPostalCode>
    <PartyRequestingRecordingState>TX</PartyRequestingRecordingState>
    <PartyRequestingRecordingStreetAddress>14925 KINGSPORT RD</PartyRequestingRecordingStreetAddress>
    <PreparerMailingAddressCity>FORT WORTH</PreparerMailingAddressCity>
    <PreparerMailingAddressPostalCode>76155</PreparerMailingAddressPostalCode>
    <PreparerMailingAddressState>TX</PreparerMailingAddressState>
    <PreparerMailingAddressStreetAddr1>14925 KINGSPORT RD</PreparerMailingAddressStreetAddr1>
    <PreparerPartyType>2</PreparerPartyType>

    <CustomSBAOrgTypeDesc>GOVERNMENT AGENCY</CustomSBAOrgTypeDesc>
    <OrgTermTypeOtherDesc>SBA</OrgTermTypeOtherDesc>

    <SBALoanInd>1</SBALoanInd>
    <SBADisasterLoanType>DLH</SBADisasterLoanType>
    <SBAAgreementOfComplianceRequiredInd>0</SBAAgreementOfComplianceRequiredInd>
    <SBAProductType>Home</SBAProductType>
    <TILARESPACombinedRediscloseInd>1</TILARESPACombinedRediscloseInd>
    <SBADisasterDate>2021-02-11</SBADisasterDate>
    <SBADisasterDesc>Severe Winter Storms</SBADisasterDesc>
    <SBARequestReconsiderationInd>1</SBARequestReconsiderationInd>
    <ApplicationDate>2025-07-16</ApplicationDate>
    <DisclosureDate>2025-07-21</DisclosureDate>
    <DisclosuresEstimated>1</DisclosuresEstimated>
    <SBALoanDeclineType>Declined</SBALoanDeclineType>
    <IntegratedDisclosurePurposeType>Home Equity Loan</IntegratedDisclosurePurposeType>
    <DocReturnedToBusinessName>U.S. SMALL BUSINESS ADMINISTRATION</DocReturnedToBusinessName>
    <DocReturnedToDeliveryType>1</DocReturnedToDeliveryType>
    <DocReturnedToEntityType>Domestic Government Unit</DocReturnedToEntityType>
    <SBAIncludePreProcessingLanguageInd>1</SBAIncludePreProcessingLanguageInd>
    <EscrowAccountNotEstablishedReasonType>2</EscrowAccountNotEstablishedReasonType>
    <DocReturnedToMailingAddressCity>FORT WORTH</DocReturnedToMailingAddressCity>
    <DocReturnedToMailingAddressPostalCode>76155</DocReturnedToMailingAddressPostalCode>
    <DocReturnedToMailingAddressState>TX</DocReturnedToMailingAddressState>
    <DocReturnedToMailingAddressStreetAddr1>14925 KINGSPORT RD</DocReturnedToMailingAddressStreetAddr1>
    <DocReturnedToPartyType>2</DocReturnedToPartyType>
    <DocReturnedToPhoneNumber>************</DocReturnedToPhoneNumber>
    <DocReturnedToPrintOptionalInd>1</DocReturnedToPrintOptionalInd>
    <PreparerBusinessName>U.S. SMALL BUSINESS ADMINISTRATION</PreparerBusinessName>
    <PreparerEntityType>DOMESTIC GOVERNMENT UNIT</PreparerEntityType>
    <OrgLegalNameAndDesc>U.S. SMALL BUSINESS ADMINISTRATION, A GOVERNMENT AGENCY</OrgLegalNameAndDesc>
    <FormStampTaxCoverPageFLExcludeInd>1</FormStampTaxCoverPageFLExcludeInd>
    <PreparerPhones>

        <PreparerPhone>
            <PhoneNumber>************</PhoneNumber>
            <Type>Business</Type>
        </PreparerPhone>

    </PreparerPhones>


    <RecordingRequirementsActualMarginsInd>1</RecordingRequirementsActualMarginsInd>


    <ClosingInformationDetails>

        <ClosingInformationDetail>
            <IntegratedDisclosureType>1</IntegratedDisclosureType>
        </ClosingInformationDetail>

        <ClosingInformationDetail>
            <IntegratedDisclosureType>1</IntegratedDisclosureType>
        </ClosingInformationDetail>

    </ClosingInformationDetails>


    <EstimatedPropertyCosts>

        <EstimatedPropertyCost>
            <IntegratedDisclosureType>1</IntegratedDisclosureType>
        </EstimatedPropertyCost>

    </EstimatedPropertyCosts>


    <IntegratedDisclosureDetails>

        <IntegratedDisclosureDetail>
            <IntegratedDisclosureType>1</IntegratedDisclosureType>
        </IntegratedDisclosureDetail>

    </IntegratedDisclosureDetails>


    <LoanOfficers>

        <LoanOfficer>
            <EmployerType>1</EmployerType>
            <Phone>************</Phone>
        </LoanOfficer>

    </LoanOfficers>


    <SBAPaymentBeginningBasedOnType>2</SBAPaymentBeginningBasedOnType>
    <SpecimenSignatureInd>1</SpecimenSignatureInd>
    <SBADisasterDeclarationType>Presidential IA</SBADisasterDeclarationType>
    <SBACaliforniaDueOnSaleLanguageDesc>Lender may, at its option, declare the entire balance of the
        Secured Debt to be immediately due and payable upon the creation of, or contract for the
        creation of, any lien, encumbrance, transfer or sale of all or any part of the Property.
        This right is subject to the restrictions imposed by federal law, as applicable.</SBACaliforniaDueOnSaleLanguageDesc>
    <Rescission>0</Rescission>
    <PreparerID>84cdc151-17ef-4048-8a86-ef7081220989</PreparerID>
    <PartyRequestingRecordingID>84cdc151-17ef-4048-8a86-ef7081220989</PartyRequestingRecordingID>
    <PartyRequestingRecordingPrintOptionalInd>1</PartyRequestingRecordingPrintOptionalInd>
    <PreparerPrintOptionalInd>1</PreparerPrintOptionalInd>
    <ExcludeVirginiaIndexingCoverSheetDeedOfTrustInd>0</ExcludeVirginiaIndexingCoverSheetDeedOfTrustInd>
    <DepositLanguage>0</DepositLanguage>
    <SBARegularPaymentPeriodType>1</SBARegularPaymentPeriodType>
    <SBAProgramType>1</SBAProgramType>
    <TaglineUserDefinedLeftDesc>202307x20250717</TaglineUserDefinedLeftDesc>
    <WitnessLinesSuppressedInd>1</WitnessLinesSuppressedInd>
    <DefaultIncludeGracePeriodsType>1</DefaultIncludeGracePeriodsType>
    <DefaultNonPaymentGraceDaysCount>60</DefaultNonPaymentGraceDaysCount>
    <DefaultOptionType>1</DefaultOptionType>
    <DefaultPaymentGraceDaysCount>60</DefaultPaymentGraceDaysCount>
    <FederalPreemptionInd>1</FederalPreemptionInd>
    <OrgCounty>TARRANT COUNTY</OrgCounty>
    <DocumentsDrawnInType>1</DocumentsDrawnInType>
    <ApplicationNumberDesc>25069257425</ApplicationNumberDesc>
    <FederalPreemptionInd>1</FederalPreemptionInd>
    <!-- END: Tags for Security Commercial> -->
    <CreditBureaus>

        <CreditBureau>

            <CreditScores index="1">
                <CreditScore>
                    <Date>2025-07-16</Date>
                    <NoticeOfActionTakenCreditScoreInd>1</NoticeOfActionTakenCreditScoreInd>
                    <RembrandtIDRef>5ceb81bc-80f2-486a-b811-1a95af5cd247</RembrandtIDRef>
                    <UsedInUnderwritingInd>1</UsedInUnderwritingInd>
                    <CreditScoreNotAvailableInd>1</CreditScoreNotAvailableInd>
                </CreditScore>
            </CreditScores>

            <City>ALLEN</City>
            <CreditScoreRangeFromNumber>300</CreditScoreRangeFromNumber>
            <CreditScoreRangeToNumber>850</CreditScoreRangeToNumber>
            <InternetAddress>www.experian.com</InternetAddress>
            <RegulatorPostalCode>75013</RegulatorPostalCode>
            <PostalCode>75013</PostalCode>
            <RegulatorState>TX</RegulatorState>
            <State>TX</State>
            <StreetAddress>P.O. BOX 4500</StreetAddress>
            <TollFreePhoneNumber>************</TollFreePhoneNumber>
            <Type>1</Type>
        </CreditBureau>

    </CreditBureaus>


    <Regulators>

        <Regulator>

            <RegulatorAddresses index="1">
                <RegulatorAddress>
                    <Value>CONSUMER RESPONSE CENTER, 600 PENNSYLVANIA AVE NW</Value>
                </RegulatorAddress>
            </RegulatorAddresses>

            <RegulatorCity>WASHINGTON</RegulatorCity>
            <RegulatorLevelType>1</RegulatorLevelType>
            <RegulatorName>FEDERAL TRADE COMMISSION</RegulatorName>
            <RegulatorPostalCode>20580</RegulatorPostalCode>
            <RegulatorState>DC</RegulatorState>
        </Regulator>

    </Regulators>


    <LoanDecisionActionReasons>

        <ActionReason>
            <IDRef>5d541e73-4566-48df-ad16-195663fb2724</IDRef>
            <ReacceptanceDueDate>2026-01-21</ReacceptanceDueDate>
            <WithdrawnReasonType>51</WithdrawnReasonType>
            <PartiesMissingIncomeTaxReturns>

            </PartiesMissingIncomeTaxReturns>
        </ActionReason>

        <ActionReason>
            <IDRef>5d541e73-4566-48df-ad16-195663fb2724</IDRef>
            <ReacceptanceDueDate>2026-01-21</ReacceptanceDueDate>
            <WithdrawnReasonType>53d</WithdrawnReasonType>
            <PartiesMissingIncomeTaxReturns>

            </PartiesMissingIncomeTaxReturns>
            <WithdrawalCodeParties>

                <WithdrawalCodeParty>
                    <FullName>Lara Snow</FullName>
                </WithdrawalCodeParty>

            </WithdrawalCodeParties>
            <CustomCreditReportWithdrawalDesc>&lt;p&gt;Custom CBR Withdrawal Language Goes
                Here&lt;/p&gt;</CustomCreditReportWithdrawalDesc>
        </ActionReason>

    </LoanDecisionActionReasons>


</Txn>