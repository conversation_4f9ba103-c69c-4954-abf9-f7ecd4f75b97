import base64
import json
import requests

# create 1 json to store configuration_details
with open("./wkfs_script_dlap/configuration.json", "r") as configuration_file:
    configuration_details = json.load(configuration_file)

# create 2nd json to store wkfs_creds_details
with open("./wkfs_script_dlap/credentials.json", "r") as credentials_file:
    wkfs_creds_details = json.load(credentials_file)

# create 3rd txt file to store package_name
with open("./wkfs_script_dlap/package_name.txt", "r") as package_file:
    package_name = package_file.read()

auth_host = "https://ct-admin.wolterskluwerfs.com"
host = "https://ct-docgensvcs.wolterskluwerfs.com"


def send_request(method, host, url, payload, headers):
    response = requests.request(
        method,
        f"{host}{url}",
        headers=headers,
        timeout=60,
        data=payload,
    )

    response = response.text
    return response


def get_access_token():
    headers = {"WKFS-ClientCertificate": configuration_details.get("wkfs_client_certificate")}
    payload = {
        "grant_type": configuration_details.get("grant_type"),
        "client_id": configuration_details.get("client_id"),
        "scope": configuration_details.get("scope"),
    }
    response = send_request(
        "POST",
        auth_host,
        f"/STS/connect/token",
        payload=payload,
        headers=headers,
    )
    return response


def generate_package(access_token):
    # Read the XML file as bytes
    with open("./wkfs_script_dlap/wkfs.xml", "rb") as xml_file:
        xml_data = xml_file.read()

    # Encode the XML data in Base64 format
    base64_data = base64.b64encode(xml_data)
    base64_data = base64_data.decode()

    # print("BASE64_DATA:", base64_data.decode())

    request = {
        "documentFormat": "PDF",
        "ancillaryOutput": [
            {
                "outputType": "ESignatureAndFieldSupport",
                "eSignatureAndFieldSupport": {
                    "eSignatureCoordinatesOnly": True,
                    "eSignatureDateSupport": True,
                    "eSignatureTooltip": "Kindly Sign here",
                    "eSignatureInitialsTooltip": "Kindly put your initials here",
                    "nonSignatureFieldCoordinatesOnly": True,
                    "eSignatureWKES": False,
                },
            }
        ],
        "transactionData": base64_data,
    }

    wkfs_package = None
    wkfs_id = configuration_details.get("wkfs_id")
    
    for package in configuration_details.get("packages"):
        wkfs_package_name = package.get("name")
        if wkfs_package_name == package_name:
            wkfs_package = package.get("package_or_packet")
            break

    request["contentIdentifier"] = f"expere://{wkfs_id}/{wkfs_package}"
    request["ezConfig"] = configuration_details.get("ezConfig")
    payload = {
        "generate": {"request": request},
    }
    headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
    account_id = configuration_details.get("account_id")

    response = send_request(
        "POST",
        host,
        f"/DocumentService/api/v1/Document/account/{account_id}/generate-synchronous",
        payload=json.dumps(payload),
        headers=headers,
    )
    with open("response_wkfs.txt", "w") as file:
        file.write(response)
    return json.loads(response)


def call_endpoint():
    configuration_details.update(wkfs_creds_details)
    response = get_access_token()
    json_response = json.loads(response)

    access_token = json_response.get("access_token", "")

    response = generate_package(access_token)
    print(json.dumps(response))

    print(response)
    with open("./wkfs_script_dlap/response.txt", "w") as f:
        f.write(json.dumps(response))

    for packet in response["generateResultsResponse"]["getGenerateResultsResult"]["documents"]["packet"]:
        if packet.get("packetName")== "initial_lcds":
            if packet["docInstances"]:
                base64str = packet["docInstances"][0]["docData"]["base64"]
                message_bytes = base64.b64decode(base64str.encode())
                
                with open("./wkfs_script_dlap/initial_lcd.pdf", "wb") as f:
                    f.write(message_bytes)

        elif packet.get("packetName")== "collateral_lcds":
            if packet["docInstances"]:
                # WHEN CREATING A PARTICULAR PACKET OUT OF THE THREE SAVE A FLAG FOR IT AS EXTRA VALIDATION/ TO SHOW THE BUTTON 
                # SO AS THE BUTTON DOES NOT BREAKS ANYTHING IF NO DOCS
                base64str_rec = packet["docInstances"][0]["docData"]["base64"]
                recording_bytes = base64.b64decode(base64str_rec.encode())
                
                with open("./wkfs_script_dlap/collateral_lcd.pdf", "wb") as f:
                    f.write(recording_bytes)
        # elif packet.get("packetName")== "All Closing Documents":
        else:
            if packet["docInstances"]:
                base64str_rec = packet["docInstances"][0]["docData"]["base64"]
                recording_bytes = base64.b64decode(base64str_rec.encode())
                
                with open("./wkfs_script_dlap/all_docs.pdf", "wb") as f:
                    f.write(recording_bytes)


call_endpoint()
