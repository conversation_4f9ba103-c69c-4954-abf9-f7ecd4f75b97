<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WKFS Document Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f5f5f7;
            color: #1d1d1f;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            color: #86868b;
        }

        .tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            background: none;
            font-size: 1rem;
        }

        .tab.active {
            background: #007aff;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #f0f0f0;
        }

        .tab-content {
            display: none;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
        }

        .tab-content.active {
            display: block;
        }

        .json-editor {
            position: relative;
            margin-bottom: 20px;
        }

        .json-editor textarea {
            width: 100%;
            min-height: 300px;
            padding: 16px;
            border: 2px solid #e5e5e7;
            border-radius: 8px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            background: #fafafa;
            transition: border-color 0.3s ease;
        }

        .json-editor textarea:focus {
            outline: none;
            border-color: #007aff;
            background: white;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #007aff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: #0056cc;
        }

        .btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #0056cc;
            transform: translateY(-1px);
        }

        .btn.secondary {
            background: #f0f0f0;
            color: #1d1d1f;
        }

        .btn.secondary:hover {
            background: #e0e0e0;
        }

        .btn.danger {
            background: #ff3b30;
        }

        .btn.danger:hover {
            background: #d70015;
        }

        .packages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .package-card {
            border: 2px solid #e5e5e7;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            background: white;
        }

        .package-card:hover {
            border-color: #007aff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .package-card.selected {
            border-color: #007aff;
            background: #f0f8ff;
        }

        .package-card h3 {
            margin-bottom: 10px;
            color: #1d1d1f;
            font-size: 1.1rem;
        }

        .package-card p {
            color: #86868b;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert.info {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #99d3ff;
        }

        .hidden {
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007aff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }

        .log-output {
            background: #1d1d1f;
            color: #f5f5f7;
            padding: 20px;
            border-radius: 8px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .pdf-viewer {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .pdf-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .pdf-icon {
            width: 60px;
            height: 60px;
            background: #ff3b30;
            border-radius: 12px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .tabs {
                flex-direction: column;
            }
            
            .packages-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WKFS Document Manager</h1>
            <p>Professional interface for managing document generation configurations</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('config')">Configuration</button>
            <button class="tab" onclick="showTab('credentials')">Credentials</button>
            <button class="tab" onclick="showTab('packages')">Package Selection</button>
            <button class="tab" onclick="showTab('execute')">Execute & Results</button>
        </div>

        <!-- Configuration Tab -->
        <div id="config" class="tab-content active">
            <h2 style="margin-bottom: 20px;">Configuration Settings</h2>
            <p style="margin-bottom: 20px; color: #86868b;">Edit the configuration JSON below. You can copy and paste your existing configuration or modify individual fields.</p>

            <div class="json-editor">
                <button class="copy-btn" onclick="copyToClipboard('configJson')">Copy</button>
                <textarea id="configJson" placeholder="Paste your configuration JSON here..."></textarea>
            </div>

            <button class="btn" onclick="saveConfiguration()">Save Configuration</button>
            <button class="btn secondary" onclick="loadConfiguration()">Load Current</button>
            <button class="btn secondary" onclick="formatJson('configJson')">Format JSON</button>
        </div>

        <!-- Credentials Tab -->
        <div id="credentials" class="tab-content">
            <h2 style="margin-bottom: 20px;">Credentials Settings</h2>
            <p style="margin-bottom: 20px; color: #86868b;">Edit the credentials JSON below. You can copy and paste your existing credentials or modify individual fields.</p>

            <div class="json-editor">
                <button class="copy-btn" onclick="copyToClipboard('credentialsJson')">Copy</button>
                <textarea id="credentialsJson" placeholder="Paste your credentials JSON here..."></textarea>
            </div>

            <button class="btn" onclick="saveCredentials()">Save Credentials</button>
            <button class="btn secondary" onclick="loadCredentials()">Load Current</button>
            <button class="btn secondary" onclick="formatJson('credentialsJson')">Format JSON</button>
        </div>

        <!-- Package Selection Tab -->
        <div id="packages" class="tab-content">
            <h2 style="margin-bottom: 20px;">Package Selection</h2>
            <p style="margin-bottom: 20px; color: #86868b;">Select the package you want to generate documents for.</p>

            <div id="packagesContainer" class="packages-grid">
                <!-- Packages will be loaded here -->
            </div>

            <div style="margin-top: 20px;">
                <strong>Selected Package: </strong>
                <span id="selectedPackage" style="color: #007aff;">None</span>
            </div>
        </div>

        <!-- Execute & Results Tab -->
        <div id="execute" class="tab-content">
            <h2 style="margin-bottom: 20px;">Execute Script & View Results</h2>

            <div style="margin-bottom: 20px;">
                <button class="btn" onclick="executeScript()">
                    <span id="executeText">Execute Script</span>
                    <span id="executeLoader" class="loading hidden"></span>
                </button>
                <button class="btn secondary" onclick="viewLogs()">View Logs</button>
                <button class="btn danger" onclick="cleanupFiles()">Cleanup Files</button>
            </div>

            <div id="alertContainer"></div>

            <div class="status-panel">
                <h3>Execution Status</h3>
                <div id="statusContent">Ready to execute</div>
            </div>

            <div id="logPanel" class="status-panel hidden">
                <h3>Execution Logs</h3>
                <div class="log-output" id="logOutput"></div>
            </div>

            <div id="pdfPanel" class="pdf-viewer hidden">
                <!-- PDF cards will be added here -->
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentConfig = {};
        let currentCredentials = {};
        let selectedPackageName = '';

        // Tab management
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');

            // Load data when switching to specific tabs
            if (tabName === 'config') {
                loadConfiguration();
            } else if (tabName === 'credentials') {
                loadCredentials();
            } else if (tabName === 'packages') {
                loadPackages();
            }
        }

        // Copy to clipboard functionality
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);
            document.execCommand('copy');

            // Show feedback
            const copyBtn = element.parentElement.querySelector('.copy-btn');
            const originalText = copyBtn.textContent;
            copyBtn.textContent = 'Copied!';
            setTimeout(() => {
                copyBtn.textContent = originalText;
            }, 2000);
        }

        // Format JSON
        function formatJson(elementId) {
            const element = document.getElementById(elementId);
            try {
                const parsed = JSON.parse(element.value);
                element.value = JSON.stringify(parsed, null, 2);
                showAlert('JSON formatted successfully!', 'success');
            } catch (error) {
                showAlert('Invalid JSON format: ' + error.message, 'error');
            }
        }

        // Load configuration from file
        function loadConfiguration() {
            // Sample configuration based on the actual file
            const sampleConfig = {
                "scope": "wk-dgs-core-read wk-ezcfg-def-data-read wk-ezcfg-pkg-pkt-read wk-dgs-core-write",
                "wkfs_id": "SummitDL202307x20250306",
                "ezConfig": {
                    "orgLOB": "Commercial Lending",
                    "orgName": "SBA Disaster Lending",
                    "orgAlias": "expere://SummitDL202307x20250306/(.*)",
                    "productNames": ["General Product"],
                    "mergeEZConfig": "LogoOnly"
                },
                "packages": [
                    {"name": "AmendedLAA", "template_xml": "los_base_template.xml", "package_or_packet": "PKGPKT.43586", "wkfs_package_name": "PKG.SBADisasterAmendedLoanAuthorizationAndAgreement"},
                    {"name": "Closing", "template_xml": "los_base_template.xml", "package_or_packet": "PKGPKT.43587", "wkfs_package_name": "PKG.SBADisasterClosing", "package_or_packet_xml": "PKG.SBADisasterClosing"},
                    {"name": "FloodDetermination", "template_xml": "los_base_template.xml", "package_or_packet": "PKGPKT.43588", "wkfs_package_name": "PKG.SBADisasterFlood"},
                    {"name": "LoanDecision", "template_xml": "los_base_template.xml", "package_or_packet": "PKGPKT.43589", "wkfs_package_name": "PKG.SBADisasterLoanDecision", "package_or_packet_xml": "PKG.SBADisasterLoanDecision"},
                    {"name": "LoanEstimate", "template_xml": "los_base_template.xml", "package_or_packet": "PKGPKT.43590", "wkfs_package_name": "PKG.SBADisasterLoanEstimate"},
                    {"name": "Pre-Closing", "template_xml": "los_base_template.xml", "package_or_packet": "PKGPKT.43591", "wkfs_package_name": "PKG.SBADisasterPre-Closing"},
                    {"name": "Processing", "template_xml": "los_base_template.xml", "package_or_packet": "PKGPKT.43592", "wkfs_package_name": "PKG.SBADisasterProcessing"},
                    {"name": "ThirdParty", "template_xml": "los_base_template.xml", "package_or_packet": "PKGPKT.43593", "wkfs_package_name": "PKG.SBADisasterThirdPartyNotices"},
                    {"name": "FloodCertificationDraft", "template_xml": "los_base_template.xml", "package_or_packet": "PKGPKT.43594", "wkfs_package_name": "PKG.SBAFloodCertification"}
                ],
                "docviewer_options": {
                    "isReadOnly": false, "trackXPathChanges": true, "isCreatePDFVisible": true, "supportRequiredData": true,
                    "isBackButtonDisabled": true, "isPackageMenuVisible": true, "isSummaryTrayVisibile": true,
                    "isCreateDocumentVisible": true, "isDocumentDrawerVisible": true, "hideSessionTimeoutErrors": true,
                    "isDocDisplayHeaderVisible": true, "isDocOptionsHeaderVisible": true, "mergeTransactionWithEmpty": true,
                    "isDocumentCompletionEnabled": true, "trackXPathChangesWithIndexAttribute": true
                }
            };

            currentConfig = sampleConfig;
            document.getElementById('configJson').value = JSON.stringify(sampleConfig, null, 2);
        }

        // Load credentials from file
        function loadCredentials() {
            const sampleCredentials = {
                "client_id": "10660.Summit Disaster Loans.*********.AccountClient.1",
                "account_id": "10660",
                "grant_type": "client_credentials",
                "wkfs_client_certificate": "MIIDSTCCAjGgAwIBAgIQAIyWidx+kmLxdgo1PYI1CzANBgkqhkiG9w0BAQ0FADAsMSowKAYDVQQDDCFTdW1taXQgRGlzYXN0ZXIgTG9hbnMgLSAxMDAxMDc2ODkwHhcNMjMwNDA3MTg1MjIxWhcNMjgwNDA3MTg1MjIxWjAsMSowKAYDVQQDDCFTdW1taXQgRGlzYXN0ZXIgTG9hbnMgLSAxMDAxMDc2ODkwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC1o2ySGrN8qNxlXxo1/prYtzFpy2LffWEBjrC+mJ/G9aoVZDAb7rHC//O86tsmD5DwD0GlSRHkpE9y4hEAtcXjfsdRe837FGbGyjTV0baHaIDph6FhqaksWSSxlT0Q+rChSzHThTxphOg6gpLWWqcfa1PVbJ3edlb3yyD+fbdVS5LGRlKv4fydCkrTZuo/Y8jDkktUyeeJo/aWqHchdsfP5Sh9cNbN4D8L0DFnYF+hEQaFD6AHtkbLxsmfGhWrzbTV9h7xSOV08RZmoGlUY4ZYaoLhRPhYay2mynEm/nlAT1keGSNYV3TvDK16tu+nM8sk64XRgMCUqNvIZuZm/+StAgMBAAGjZzBlMGMGA1UdIwRcMFqAFHD9Pme0f6t0WQwh9QbspfSbFmcZoTCkLjAsMSowKAYDVQQDDCFTdW1taXQgRGlzYXN0ZXIgTG9hbnMgLSAxMDAxMDc2ODmCEACMloncfpJi8XYKNT2CNQswDQYJKoZIhvcNAQENBQADggEBAAcaeeQjIXMltHtH25TyoU+//G7KLxhhLG989B+BHPJKAn18wxJgS0Xw77UD9zZxS+6X1aDDxet9CD6y/ew4HzjISVSDw3kzr5fHEzj2xijZQ3zhD+Gn4kKo27zk3Eva9UpczdJZmFwRYjgdA3tq9ttEiO4EV9mxaK6MMep2JLPvpVj7SVaeAWHwsw9HYlydivUWU44M59ch538dQ3R08bce8VxdorGblDVMFFkxITtDVyyR6XT+3lxeBIKjRRruYqPhiRoxFZ5dG+wSS6jnp216V5cSoSilufMg8hvgEZ0ZgMXO90h/xIDIGWvcwe6DaZ+fW8XRZSj6ysdZEraNU7A="
            };

            currentCredentials = sampleCredentials;
            document.getElementById('credentialsJson').value = JSON.stringify(sampleCredentials, null, 2);
        }

        // Save configuration
        function saveConfiguration() {
            try {
                const configText = document.getElementById('configJson').value;
                const config = JSON.parse(configText);
                currentConfig = config;

                // In a real implementation, this would save to the server
                showAlert('Configuration saved successfully! (Note: This is a demo - changes are not persisted)', 'success');

                // Update packages display if on packages tab
                if (document.getElementById('packages').classList.contains('active')) {
                    loadPackages();
                }
            } catch (error) {
                showAlert('Error saving configuration: ' + error.message, 'error');
            }
        }

        // Save credentials
        function saveCredentials() {
            try {
                const credentialsText = document.getElementById('credentialsJson').value;
                const credentials = JSON.parse(credentialsText);
                currentCredentials = credentials;

                // In a real implementation, this would save to the server
                showAlert('Credentials saved successfully! (Note: This is a demo - changes are not persisted)', 'success');
            } catch (error) {
                showAlert('Error saving credentials: ' + error.message, 'error');
            }
        }

        // Load packages
        function loadPackages() {
            const container = document.getElementById('packagesContainer');
            container.innerHTML = '';

            if (!currentConfig.packages || currentConfig.packages.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #86868b;">No packages available. Please configure packages first.</p>';
                return;
            }

            currentConfig.packages.forEach(pkg => {
                const card = document.createElement('div');
                card.className = 'package-card';
                card.onclick = () => selectPackage(pkg.name);

                card.innerHTML = `
                    <h3>${pkg.name}</h3>
                    <p><strong>Template:</strong> ${pkg.template_xml}</p>
                    <p><strong>Package/Packet:</strong> ${pkg.package_or_packet}</p>
                    <p><strong>WKFS Package:</strong> ${pkg.wkfs_package_name}</p>
                `;

                if (selectedPackageName === pkg.name) {
                    card.classList.add('selected');
                }

                container.appendChild(card);
            });
        }

        // Select package
        function selectPackage(packageName) {
            selectedPackageName = packageName;
            document.getElementById('selectedPackage').textContent = packageName;

            // Update visual selection
            const cards = document.querySelectorAll('.package-card');
            cards.forEach(card => card.classList.remove('selected'));
            event.currentTarget.classList.add('selected');

            showAlert(`Package "${packageName}" selected successfully!`, 'success');
        }

        // Execute script
        function executeScript() {
            if (!selectedPackageName) {
                showAlert('Please select a package first!', 'error');
                return;
            }

            // Show loading state
            document.getElementById('executeText').textContent = 'Executing...';
            document.getElementById('executeLoader').classList.remove('hidden');

            // Update status
            document.getElementById('statusContent').innerHTML = `
                <div style="color: #007aff;">
                    <strong>Status:</strong> Executing script for package "${selectedPackageName}"<br>
                    <strong>Time:</strong> ${new Date().toLocaleString()}
                </div>
            `;

            // Simulate script execution
            setTimeout(() => {
                // Reset button state
                document.getElementById('executeText').textContent = 'Execute Script';
                document.getElementById('executeLoader').classList.add('hidden');

                // Show success
                showAlert('Script executed successfully! Check the results below.', 'success');

                // Update status
                document.getElementById('statusContent').innerHTML = `
                    <div style="color: #28a745;">
                        <strong>Status:</strong> Execution completed successfully<br>
                        <strong>Package:</strong> ${selectedPackageName}<br>
                        <strong>Completed:</strong> ${new Date().toLocaleString()}
                    </div>
                `;

                // Show PDF results
                showPDFResults();

                // Show logs
                document.getElementById('logPanel').classList.remove('hidden');
                document.getElementById('logOutput').textContent = `
[${new Date().toISOString()}] Starting WKFS document generation...
[${new Date().toISOString()}] Loading configuration from configuration.json
[${new Date().toISOString()}] Loading credentials from credentials.json
[${new Date().toISOString()}] Selected package: ${selectedPackageName}
[${new Date().toISOString()}] Authenticating with WKFS API...
[${new Date().toISOString()}] Authentication successful
[${new Date().toISOString()}] Generating documents...
[${new Date().toISOString()}] Document generation completed
[${new Date().toISOString()}] Generated PDFs:
[${new Date().toISOString()}]   - initial_lcd.pdf
[${new Date().toISOString()}]   - collateral_lcd.pdf
[${new Date().toISOString()}]   - all_docs.pdf
[${new Date().toISOString()}] Process completed successfully
                `;
            }, 3000);
        }

        // Show PDF results
        function showPDFResults() {
            const pdfPanel = document.getElementById('pdfPanel');
            pdfPanel.classList.remove('hidden');

            const pdfs = [
                { name: 'Initial LCD', filename: 'initial_lcd.pdf', description: 'Initial loan closing documents' },
                { name: 'Collateral LCD', filename: 'collateral_lcd.pdf', description: 'Collateral loan closing documents' },
                { name: 'All Documents', filename: 'all_docs.pdf', description: 'Complete document package' }
            ];

            pdfPanel.innerHTML = pdfs.map(pdf => `
                <div class="pdf-card">
                    <div class="pdf-icon">PDF</div>
                    <h4>${pdf.name}</h4>
                    <p style="margin-bottom: 15px; color: #86868b;">${pdf.description}</p>
                    <button class="btn secondary" onclick="openPDF('${pdf.filename}')">Open PDF</button>
                    <button class="btn secondary" onclick="downloadPDF('${pdf.filename}')">Download</button>
                </div>
            `).join('');
        }

        // Open PDF (placeholder)
        function openPDF(filename) {
            showAlert(`Opening ${filename}... (In a real implementation, this would open the PDF file)`, 'info');
        }

        // Download PDF (placeholder)
        function downloadPDF(filename) {
            showAlert(`Downloading ${filename}... (In a real implementation, this would download the PDF file)`, 'info');
        }

        // View logs
        function viewLogs() {
            const logPanel = document.getElementById('logPanel');
            if (logPanel.classList.contains('hidden')) {
                logPanel.classList.remove('hidden');
            } else {
                logPanel.classList.add('hidden');
            }
        }

        // Cleanup files
        function cleanupFiles() {
            if (confirm('Are you sure you want to cleanup all generated files? This action cannot be undone.')) {
                // Hide PDF panel
                document.getElementById('pdfPanel').classList.add('hidden');

                // Clear logs
                document.getElementById('logOutput').textContent = '';
                document.getElementById('logPanel').classList.add('hidden');

                // Reset status
                document.getElementById('statusContent').textContent = 'Ready to execute';

                showAlert('Files cleaned up successfully!', 'success');
            }
        }

        // Show alert
        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert ${type}`;
            alert.textContent = message;

            container.appendChild(alert);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadConfiguration();
            loadCredentials();
        });
    </script>
</body>
</html>
