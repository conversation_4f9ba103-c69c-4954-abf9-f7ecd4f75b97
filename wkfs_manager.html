<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WKFS Document Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f5f5f7;
            color: #1d1d1f;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            color: #86868b;
        }

        .tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            background: none;
            font-size: 1rem;
        }

        .tab.active {
            background: #007aff;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #f0f0f0;
        }

        .tab-content {
            display: none;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #1d1d1f;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e5e7;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007aff;
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }

        .btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #0056cc;
            transform: translateY(-1px);
        }

        .btn.secondary {
            background: #f0f0f0;
            color: #1d1d1f;
        }

        .btn.secondary:hover {
            background: #e0e0e0;
        }

        .btn.danger {
            background: #ff3b30;
        }

        .btn.danger:hover {
            background: #d70015;
        }

        .packages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .package-card {
            border: 2px solid #e5e5e7;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .package-card:hover {
            border-color: #007aff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .package-card.selected {
            border-color: #007aff;
            background: #f0f8ff;
        }

        .package-card h3 {
            margin-bottom: 10px;
            color: #1d1d1f;
        }

        .package-card p {
            color: #86868b;
            font-size: 0.9rem;
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }

        .status-panel h3 {
            margin-bottom: 15px;
            color: #1d1d1f;
        }

        .log-output {
            background: #1d1d1f;
            color: #f5f5f7;
            padding: 20px;
            border-radius: 8px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .pdf-viewer {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .pdf-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .pdf-card h4 {
            margin-bottom: 15px;
            color: #1d1d1f;
        }

        .pdf-icon {
            width: 60px;
            height: 60px;
            background: #ff3b30;
            border-radius: 12px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert.info {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #99d3ff;
        }

        .hidden {
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007aff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .packages-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WKFS Document Manager</h1>
            <p>Professional interface for managing document generation configurations</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('config')">Configuration</button>
            <button class="tab" onclick="showTab('credentials')">Credentials</button>
            <button class="tab" onclick="showTab('packages')">Package Selection</button>
            <button class="tab" onclick="showTab('execute')">Execute & Results</button>
        </div>

        <!-- Configuration Tab -->
        <div id="config" class="tab-content active">
            <h2 style="margin-bottom: 20px;">Configuration Settings</h2>
            
            <div class="two-column">
                <div class="form-group">
                    <label for="scope">Scope</label>
                    <textarea id="scope" placeholder="Enter scope permissions"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="wkfs_id">WKFS ID</label>
                    <input type="text" id="wkfs_id" placeholder="Enter WKFS ID">
                </div>
            </div>

            <div class="form-group">
                <label for="ezconfig">EZ Config (JSON)</label>
                <textarea id="ezconfig" placeholder="Enter EZ Config JSON"></textarea>
            </div>

            <div class="form-group">
                <label for="docviewer_options">Document Viewer Options (JSON)</label>
                <textarea id="docviewer_options" placeholder="Enter Document Viewer Options JSON"></textarea>
            </div>

            <button class="btn" onclick="saveConfiguration()">Save Configuration</button>
            <button class="btn secondary" onclick="loadConfiguration()">Load Current</button>
        </div>
